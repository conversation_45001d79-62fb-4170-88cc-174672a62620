package com.ruoyi.fat.controller;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fat.domain.FatEatItem;
import com.ruoyi.fat.service.IFatEatItemService;
import com.ruoyi.web.domain.CaszPatientCenter;
import com.ruoyi.web.service.ICaszPatientCenterService;
import com.ruoyi.web.service.ICaszPatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fat.domain.FatEat;
import com.ruoyi.fat.service.IFatEatService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 饮食日记Controller
 * 
 * <AUTHOR>
 * @date 2024-08-25
 */
@RestController
@RequestMapping("/sign/eat")
public class FatEatController extends BaseController
{
    @Autowired
    private IFatEatService fatEatService;
    @Autowired
    private ICaszPatientService patientService;
    @Autowired
    private IFatEatItemService eatItemService;
    @Autowired
    private ICaszPatientCenterService patientCenterService;
    /**
     * 查询饮食日记列表
     */
//    @PreAuthorize("@ss.hasPermi('sign:eat:list')")
    @GetMapping("/list")
    public TableDataInfo list(FatEat fatEat)
    {
        startPage();
        List<FatEat> list = fatEatService.selectFatEatList(fatEat);
        if(list != null && list.size()>0){
            for (FatEat eat : list) {
                LambdaQueryWrapper<FatEatItem> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(FatEatItem::getEatId,eat.getId());
                List<FatEatItem> items = eatItemService.list(wrapper);
                if(items != null && items.size()>0){
                    double sum = items.stream().mapToDouble(item -> item.getEnergy().doubleValue()).sum();
                    eat.setEnergy(BigDecimal.valueOf(sum));
                    double sum1 = items.stream().mapToDouble(item -> item.getFat().doubleValue()).sum();
                    eat.setFat(BigDecimal.valueOf(sum1));
                    double sum2 = items.stream().mapToDouble(item -> item.getProtein().doubleValue()).sum();
                    eat.setProtein(BigDecimal.valueOf(sum2));
                }else{
                    eat.setEnergy(BigDecimal.ZERO);
                    eat.setFat(BigDecimal.ZERO);
                    eat.setProtein(BigDecimal.ZERO);
                }
            }
        }
        return getDataTable(list);
    }

    @GetMapping("/userList")
    public AjaxResult userList(FatEat eat){
        String idNo = eat.getIdNo();
        if(!StringUtils.isNotEmpty(idNo)){
            idNo = getUsername();
        }
        if (idNo.length() != 18){
            CaszPatientCenter caszPatientCenter = patientCenterService.selectCaszPatientCenterByPhone(getUsername());
            idNo=caszPatientCenter.getIdNo();
        }
        if(StringUtils.isNotEmpty(idNo)){
            LambdaQueryWrapper<FatEat> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatEat::getIdNo,idNo);
            wrapper.eq(FatEat::getEatTime,eat.getEatTime());
            wrapper.orderByAsc(FatEat::getEatType);

            List<FatEat> list = fatEatService.list(wrapper);
            if(list!=null){
                for (FatEat fatEat : list) {
                    LambdaQueryWrapper<FatEatItem> itemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    itemLambdaQueryWrapper.eq(FatEatItem::getEatId,fatEat.getId());
                    itemLambdaQueryWrapper.orderByAsc(BaseEntity::getCreateTime);
                    List<FatEatItem> list1 = eatItemService.list(itemLambdaQueryWrapper);
                    fatEat.setFatEatItemList(list1);
                }
            }
            return success(list);
        }
        return success();
    }

    @GetMapping("/userPageList")
    public TableDataInfo userPageList(FatEat eat){
        String idNo = eat.getIdNo();
        if(StringUtils.isNotEmpty(idNo)){
            LambdaQueryWrapper<FatEat> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatEat::getIdNo,idNo);
            wrapper.orderByDesc(FatEat::getEatTime);
            wrapper.orderByDesc(FatEat::getEatType);
            startPage();
            List<FatEat> list = fatEatService.list(wrapper);
            if(list!=null){
                for (FatEat fatEat : list) {
                    LambdaQueryWrapper<FatEatItem> itemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    itemLambdaQueryWrapper.eq(FatEatItem::getEatId,fatEat.getId());
                    itemLambdaQueryWrapper.orderByAsc(BaseEntity::getCreateTime);
                    List<FatEatItem> list1 = eatItemService.list(itemLambdaQueryWrapper);
                    fatEat.setFatEatItemList(list1);
                }
            }
            return getDataTable(list);
        }
        return getDataTable(null);
    }

    /**
     * 导出饮食日记列表
     */
//    @PreAuthorize("@ss.hasPermi('sign:eat:export')")
    @Log(title = "饮食日记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FatEat fatEat)
    {
        List<FatEat> list = fatEatService.selectFatEatList(fatEat);
        ExcelUtil<FatEat> util = new ExcelUtil<FatEat>(FatEat.class);
        util.exportExcel(response, list, "饮食日记数据");
    }

    /**
     * 获取饮食日记详细信息
     */
//    @PreAuthorize("@ss.hasPermi('sign:eat:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(fatEatService.selectFatEatById(id));
    }

    /**
     * 新增饮食日记
     */
//    @PreAuthorize("@ss.hasPermi('sign:eat:add')")
    @Log(title = "饮食日记", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FatEat fatEat)
    {
        return toAjax(fatEatService.save(fatEat));
    }

    @Log(title = "饮食日记", businessType = BusinessType.INSERT)
    @PostMapping("/addEat")
    public AjaxResult addEat(@RequestBody FatEat fatEat)
    {
        fatEat.setCreateTime(new Date());
        String idNo = getUsername();
//        CaszPatient byIdNo = patientService.getByIdNo(idNo);
        if(StringUtils.isNotEmpty(idNo)){
//            fatEat.setPatientId(byIdNo.getId());
//            fatEat.setPatientName(byIdNo.getName());
            fatEat.setIdNo(idNo);
            //是否已经存在记录
            LambdaQueryWrapper<FatEat> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatEat::getIdNo,idNo);
            wrapper.eq(FatEat::getEatType,fatEat.getEatType());
            wrapper.eq(FatEat::getEatTime,fatEat.getEatTime());
            fatEatService.remove(wrapper);
            if ("N".equals(fatEat.getEatIf())) {
                LambdaQueryWrapper<FatEatItem> eatItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                eatItemLambdaQueryWrapper.eq(FatEatItem::getIdNo,idNo);
                eatItemLambdaQueryWrapper.eq(FatEatItem::getEatType,fatEat.getEatType());
                eatItemLambdaQueryWrapper.eq(FatEatItem::getEatTime,fatEat.getEatTime());
                eatItemService.remove(eatItemLambdaQueryWrapper);
            }
        }
        //有饮食项才保存
        LambdaQueryWrapper<FatEatItem> eatItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        eatItemLambdaQueryWrapper.eq(FatEatItem::getIdNo,idNo);
        eatItemLambdaQueryWrapper.eq(FatEatItem::getEatType,fatEat.getEatType());
        eatItemLambdaQueryWrapper.eq(FatEatItem::getEatTime,fatEat.getEatTime());
        List<FatEatItem> list = eatItemService.list(eatItemLambdaQueryWrapper);
        if((list != null && list.size()>0) || "N".equals(fatEat.getEatIf())){
            boolean save = fatEatService.save(fatEat);

            if(list != null){
                for (FatEatItem fatEatItem : list) {
                    fatEatItem.setEatId(fatEat.getId());
                    eatItemService.updateById(fatEatItem);
                }
            }

            return toAjax(save);
        }else{
            return success();
        }
    }

    /**
     * 修改饮食日记
     */
//    @PreAuthorize("@ss.hasPermi('sign:eat:edit')")
    @Log(title = "饮食日记", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FatEat fatEat)
    {
        return toAjax(fatEatService.updateFatEat(fatEat));
    }

    /**
     * 删除饮食日记
     */
//    @PreAuthorize("@ss.hasPermi('sign:eat:remove')")
    @Log(title = "饮食日记", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(fatEatService.deleteFatEatByIds(ids));
    }
}
