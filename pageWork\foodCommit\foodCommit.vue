<template>
	<view class="food-commit-container">
		<Navbar :hideBtn="false" title="饮食记录" bgColor="#b7bbfa" :h5Show="false" :fixed="true" :titleStyle="{color:'#FFFFFF', fontSize:'16px', fontWeight:'500'}"></Navbar>

		<!-- 吸顶的头部区域 -->
		<view class="sticky-top-container">
			<!-- 日期选择器 -->
			<view class="foodCommit-header">
				<u-icon name="arrow-leftward" @click="preDay" color="#333333" size="25"></u-icon>
				<view class="date-selector">
					<u-icon name="calendar" color="#4f41ac" size="28"></u-icon>
					<view @click="dateShow=true" class="date-text">{{nowDateStr?nowDateStr:''}}</view>
				</view>
				<u-icon name="arrow-rightward" @click="nextDay" color="#333333" size="25"></u-icon>
			</view>

			<!-- 营养总览卡片 -->
			<view class="summary-card-container" style="margin-top: -5px;">
				<uni-card :is-shadow="true" class="summary-card">
					<view>
						<view class="stats-icon-container">
							<u-icon @click="toEatStatics"
								name="https://fat-1323860534.cos.ap-nanjing.myqcloud.com/weixin/statics.png" color="#4f41ac"
								size="22"></u-icon>
						</view>
						<view class="nutrition-summary">
							<view class="summary-item">
								<view class="summary-label">饮食摄入</view>
								<view class="summary-value">{{nutrSumData.energy?nutrSumData.energy:''}}<span>kcal</span></view>
							</view>

							<view class="progress-container">
								<arprogress :percent="percent"><text>{{percent?percent:''}}%</text></arprogress>
							</view>
							<view class="summary-item">
								<view class="summary-label">推荐摄入</view>
								<view class="summary-value">{{medNutri?medNutri.energySuggest1:0}}<span>kcal</span></view>
							</view>
						</view>
						<view class="nutrition-details">
							<view class="detail-item">
								<view class="detail-label">蛋白质</view>
								<view class="progress-bar">
									<view class="progress-fill"
										:style="{width:nutrPecWidth('protein')}">
									</view>
								</view>
								<view class="detail-value">
									<span>{{nutrSumData.protein?nutrSumData.protein:''}}g</span>
								</view>
							</view>
							<view class="detail-item">
								<view class="detail-label">脂肪</view>
								<view class="progress-bar">
									<view class="progress-fill"
										:style="{width:nutrPecWidth('fat')}">
									</view>
								</view>
								<view class="detail-value">
									<span>{{nutrSumData.fat?nutrSumData.fat:''}}g</span>
								</view>
							</view>
							<view class="detail-item">
								<view class="detail-label">碳水化合物</view>
								<view class="progress-bar">
									<view class="progress-fill"
										:style="{width:nutrPecWidth('ch2o')}">
									</view>
								</view>
								<view class="detail-value">
									<span>{{nutrSumData.ch2o?nutrSumData.ch2o:''}}g</span>
								</view>
							</view>
						</view>
					</view>
				</uni-card>
			</view>
		</view>

		<!-- 可滚动的食物列表区域 -->
		<scroll-view scroll-y="true" class="scrollable-content" :style="{height: scrollViewHeight}">
			<view v-if="hasEatData" class="food-list-container">
			<uni-card v-for="userEat in eatList" :key="userEat.id" v-if="userEat.eatIf=='Y'"
				:title="eat[userEat.eatType]" :extra="jiSuanEnergy(userEat)" class="food-card">
				<view v-for="item in userEat.fatEatItemList" :key="item.id"
					v-if="userEat.fatEatItemList && userEat.fatEatItemList.length>0">
					<view @click="handleItemClick(item)" class="food-item">
						<view class="food-image">
							<!-- AI识别的图片使用自定义样式，支持预览 -->
							<view v-if="item.dataType == 'AI'" class="ai-food-image" @click.stop="previewImage(item)">
								<image :src="getFoodImageSrc(item)" mode="aspectFill" class="ai-image"></image>
							</view>
							<!-- 普通录入使用原来的头像组件 -->
							<u-avatar v-else shape="square" size="60" :src="getFoodImageSrc(item)"></u-avatar>
						</view>
						<view class="food-info">
							<view class="food-name">{{getFoodName(item)}}</view>
							<u-gap height="8"></u-gap>
							<view class="food-details">{{getFoodDetails(item)}}</view>
						</view>
						<view class="food-action">
							<uni-icons type="right" color="#999" size="20"></uni-icons>
						</view>
					</view>
				</view>
			</uni-card>

				<view class="action-buttons">
					<button class="action-button" @click="toNutrAnalysis"
						type="primary">营养分析</button>
					<button class="action-button" @click="toNutrRanking"
						type="primary">营养摄入排行榜</button>
				</view>
			</view>

			<u-empty v-else mode="car" text="还没有记录 请点击下方按钮添加饮食信息" class="empty-container" icon="http://cdn.uviewui.com/uview/empty/car.png">
			</u-empty>
		</scroll-view>

		<u-tabbar :value="value1" @change="change1" :fixed="true" :placeholder="false" :safeAreaInsetBottom="true" class="meal-tabbar">
			<view class="meal-tabs">
				<view v-for="i,index in eat" :key="i" class="meal-tab-item" @click="editFood(index)">
					<u-icon v-if="eatStatus[index]=='N'" name="plus" size="20" class="meal-icon"></u-icon>
					<u-icon v-else name="checkmark-circle-fill" color="#4f41ac" size="20" class="meal-icon"></u-icon>
					<view class="meal-name">{{i}}</view>
				</view>
			</view>
		</u-tabbar>

		<u-calendar ref="calendar" @close="dateShow=false" :round="10" :minDate="minDate" :maxDate="maxDate"
			:closeOnClickOverlay="true" :show="dateShow" mode="single" @confirm="confirmDate">
		</u-calendar>

		<u-popup :show="popShow" mode="bottom" style="z-index: 10080;" @close="popShow=false" :round="0">
			<view class="popup-content" style="margin: 0;">
				<view class="popup-header">
					<view class="popup-title">食物详情</view>
					<view class="popup-close" style="margin-right: 10rpx;" @click="popShow=false">
						<u-icon name="close" color="#999" size="24"></u-icon>
					</view>
				</view>
				
				<view style="padding: 0;">
					<!-- 食物标题 -->
					<view style="margin: 10rpx 10rpx 10rpx 20rpx;">
						<view style="font-size: 30rpx; font-weight: bold; color: #333; margin-bottom: 8rpx;">{{showFood.foodName}}</view>
						<view style="font-size: 24rpx; color: #666;">标准份量: <text style="color: #4f41ac; font-weight: bold;">{{ showFood.weight }}g</text></view>
					</view>

					<!-- 营养成分 -->
 					<view class="nutrition-card">
						<view style="font-size: 26rpx; font-weight: bold; color: #333; margin-bottom: 12rpx;">营养成分</view>
						<view class="nutrition-grid">
							<view class="nutrition-item">
								<view class="nutrition-label">能量</view>
								<view class="nutrition-value">{{curItem.energy}} kcal</view>
							</view>
							<view class="nutrition-item">
								<view class="nutrition-label">蛋白质</view>
								<view class="nutrition-value">{{curItem.protein}} g</view>
							</view>
							<view class="nutrition-item">
								<view class="nutrition-label">脂肪</view>
								<view class="nutrition-value">{{curItem.fat}} g</view>
							</view>
							<view class="nutrition-item">
								<view class="nutrition-label">碳水化合物</view>
								<view class="nutrition-value">{{curItem.ch2o}} g</view>
							</view>
						</view>
					</view>
					
					<!-- 重量输入 -->
					<view class="weight-input-container">
						<view style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10rpx;">
							<view style="font-size: 26rpx; font-weight: bold; color: #333;">设置重量</view>
							<view style="display: flex; align-items: center;">
								<image style="width: 32rpx; height: 32rpx; margin-right: 8rpx;"
									src="https://mobile-1323860534.cos.ap-guangzhou.myqcloud.com/fat/weight.png" />
								<text style="color: #4f41ac; font-size: 24rpx;">估算重量</text>
							</view>
						</view>
						
						<view style="font-size: 24rpx; color: #ff5252; margin-bottom: 10rpx;">
							*请输入食物的重量
						</view>
						
						<view class="weight-input-wrapper">
							<input v-if="curItem.weightType=='克'" v-model="curItem.weight" class="weight-input" />
							<input v-if="curItem.weightType=='份'" v-model="curItem.portionNum" class="weight-input" />
							<text style="margin-left: 16rpx; font-size: 28rpx; color: #333;">{{curItem.weightType}}</text>
						</view>
						
						<view class="weight-type-selector">
							<text class="weight-type-label">克</text>
							<u-switch space="2" size="35" active-value="份" inactive-value="克" v-model="curItem.weightType"
								activeColor="#f9ae3d" inactiveColor="#4f41ac">
							</u-switch>
							<text class="weight-type-label">份</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view style="display: flex; justify-content: space-between; margin: 20rpx 10rpx 10rpx 20rpx;">
						<button style="width: 30%; background-color: #f5f5f5; color: #666; font-size: 26rpx; height: 60rpx; line-height: 60rpx; border: 1rpx solid #ddd;" @click="removeItem">删除</button>
						<button type="success" style="width: 65%; background-color: #4f41ac; color: #ffffff; font-size: 26rpx; height: 60rpx; line-height: 60rpx;" @click="addFoodItem">确定</button>
					</view>
				</view>
			</view>
		</u-popup>
 
		<ShareCanvas ref="ShareCanvas" />
	</view>
</template>

<script>
	import arprogress from '@/pageWork/components/ar-circle-progress/index.vue'
	import dayjs from 'dayjs'
	import * as EatApi from "@/api/work/eat.js"
	import * as FoodApi from "@/api/work/food.js"
	import appConfig from "@/common/config"
	import ShareCanvas from "@/pageWork/components/shareCanvas.vue"
	import * as PatientApi from "@/api/patient.js"
	import Navbar from '@/components/navbar/Navbar'
	export default {
		components: {
			ShareCanvas,
			arprogress,
			Navbar
		},
		data() {
			return {
				patientId: '',
				patientCenter: null,
				percent: 0,
				foodItems: [],
				showFood: {},
				curItem: {},
				popShow: false,
				eat: ['早餐', '早加', '午餐', '午加', '晚餐', '晚加'],
				eatStatus: ['N', 'N', 'N', 'N', 'N', 'N'],
				chartData: {},
				//您可以通过修改 config-ucharts.js 文件中下标为 ['arcbar'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
				opts: {
					color: ["#1890FF"],
					padding: undefined,
					title: {
						name: "60%",
						fontSize: 25,
						color: ""
					},
					subtitle: {
						name: "比例",
						fontSize: 18,
						color: "#666666"
					},
					extra: {
						arcbar: {
							type: "default",
							width: 8,
							backgroundColor: "#E9E9E9",
							startAngle: 0.75,
							endAngle: 0.25,
							gap: 2,
							linearType: "custom"
						}
					}
				},
				popShow: false,
				dateShow: false,
				nowDate: null,
				medNutri: {},
				eatList: [],
				nutrSumData: {
					energy: 0,
					protein: 0,
					fat: 0,
					ch2o: 0
				},
				scrollViewHeight: 'calc(100vh - 400rpx)', // 滚动区域高度
				recordedDates: [] // 存储有饮食记录的日期列表
			}
		},
		onReady() {
			console.log(this.$store.state)
			// let sevenDaysAgo_time = dayjs().subtract(1, 'day')
			// console.log(sevenDaysAgo_time.format('MM月DD日'))
			// 如果需要兼容微信小程序的话，需要用此写法
			this.$refs.calendar.setFormatter(this.formatter)
		},
		mounted() {
			this.calculateScrollHeight();
		},
		async onShareAppMessage(res) {
			const id = '441212';
			const nickname = '测';
			const avatar = '32';
			console.log('分享今日饮食');
			const item = {
				image: 'https://fp.caszmt.com/profile/upload/2024/08/22/米饭_20240822193345A001.jpg',
				energy: '112',
				fes: '56',
				dinner: 4,
				storeName: '今日饮食'
			}
			let _this = this;
			console.log(this.$refs.ShareCanvas)
			try {
				uni.showLoading({
					title: '分享信息生成中',
					mask: true
				})
				const imageUrl = await _this.$refs.ShareCanvas.setShareCanvas({
					...item,
					avatar,
					nickname,
					pinkId: id
				}) // 用不同的画布画样式，就调对应的方法名，注意里面需要的参数要传对
				uni.hideLoading()
				return {
					title: '今日饮食',
					path: '//pageWork/foodCommit/foodCommit', // 这里是你的分享里面的跳转地址
					imageUrl: imageUrl || ''
				}
			} catch (error) {
				uni.hideLoading()
			}
		},
		onShow() {
			this.nowDate = dayjs()
			this.patientId = this.$store.state.unit.unionId
			// this.getServerData();
			//查询干预里的营养建议
			this.getUserCurInte()
			//查询用户的餐饮记录
			this.getUserEatList()
			//获取有记录的日期列表
			this.getRecordedDates()
		},
		computed: {
			nowDateStr() {
				if (this.nowDate) {
					return this.nowDate.format('MM月DD日')
				}
				return ''
			},
			maxDate() {
				console.log(dayjs().add(1, 'day').format('YYYY-MM-DD'))
				return dayjs().format('YYYY-MM-DD') + ' 23:59:59'
			},
			minDate() {
				return dayjs().subtract(30, 'day').format('YYYY-MM-DD')
			},

			hasEatData() {
				if (this.eatList) {
					let e = this.eatList.filter(item => item.eatIf == 'Y');
					return e && e.length > 0
				}
				return false;
			},
			nutrPecWidth() {
				return function(type) {
					if (!this.medNutri) {
						return '0%';
					}
					if (this.medNutri[type + 'Suggest1'] <= 0 && this.nutrSumData[type] > 0) {
						return '100%'
					}
					if (this.medNutri[type + 'Suggest1'] > 0 && this.nutrSumData[type] > 0) {
						return this.medNutri[type + 'Suggest1'] / this.nutrSumData[type] * 100 + '%';
					}
					return '0%';
				}
			}
		},
		watch: {
			nowDate: {
				handler(newVal, oldVal) {
					if (newVal && newVal != oldVal) {

						// this.getServerData();
						//查询干预里的营养建议
						this.getUserCurInte()
						//查询用户的餐饮记录
						this.getUserEatList()
					}
				},
				immediate: true
			},
			curItem: {
				handler(newVal, oldVal) {
					console.log(newVal)
					if (newVal && newVal.weightType && newVal.eatIf != 'N') {
						let weight = 0;
						if (newVal.weightType == "克") {
							weight = newVal.weight;
						} else {
							weight = newVal.portionNum * this.showFood.weight
						}
						console.log("当前食物重量是：" + weight)
						let a = weight / this.showFood.weight
						console.log("倍数：" + a)
						if (weight != undefined) {
							//能量、蛋白质、脂肪、碳水化合物
							this.curItem.energy = (a * this.showFood.energy).toFixed(1)
							this.curItem.protein = (a * this.showFood.protein).toFixed(1)
							this.curItem.fat = (a * this.showFood.fat).toFixed(1)
							this.curItem.ch2o = (a * this.showFood.ch2o).toFixed(1)

							this.curItem.df = (a * this.showFood.df).toFixed(1)
							this.curItem.va = (a * this.showFood.va).toFixed(1)
							this.curItem.vc = (a * this.showFood.vc).toFixed(1)
							this.curItem.ve = (a * this.showFood.ve).toFixed(1)
							this.curItem.ca = (a * this.showFood.ca).toFixed(1)
							this.curItem.pp = (a * this.showFood.pp).toFixed(1)
							this.curItem.kk = (a * this.showFood.kk).toFixed(1)
							this.curItem.na = (a * this.showFood.na).toFixed(1)
							this.curItem.fe = (a * this.showFood.fe).toFixed(1)
							this.curItem.zn = (a * this.showFood.zn).toFixed(1)
							this.curItem.ii = (a * this.showFood.ii).toFixed(1)
						}


					}
				},
				deep: true
			},
			recordedDates: {
				handler(newVal) {
					// 当有记录的日期数据更新时，重新设置日历的formatter
					if (newVal && newVal.length > 0) {
						this.$nextTick(() => {
							if (this.$refs.calendar) {
								this.$refs.calendar.setFormatter(this.formatter)
							}
						})
					}
				},
				immediate: false
			},
		},
		methods: {
			// 计算滚动区域高度
			calculateScrollHeight() {
				const systemInfo = uni.getSystemInfoSync();
				const windowHeight = systemInfo.windowHeight;
				const statusBarHeight = systemInfo.statusBarHeight || 0;
				const navbarContentHeight = 44; // 导航栏内容高度
				const totalNavbarHeight = statusBarHeight + navbarContentHeight;

				// 转换rpx到px的比例
				const rpxToPx = systemInfo.windowWidth / 750;
				const stickyHeight = 550 * rpxToPx; // 调整吸顶区域高度计算，适应紧凑布局
				const tabbarHeight = 120 * rpxToPx; // 底部tabbar高度
				const scrollHeight = windowHeight - totalNavbarHeight - stickyHeight - tabbarHeight;
				this.scrollViewHeight = Math.max(scrollHeight, 200) + 'px'; // 确保最小高度

				console.log('高度计算:', {
					windowHeight,
					statusBarHeight,
					navbarContentHeight,
					totalNavbarHeight,
					stickyHeight: stickyHeight,
					tabbarHeight: tabbarHeight,
					scrollHeight
				});
			},

			// 统一处理食物项点击事件
			handleItemClick(item) {
				// 所有食物项点击都显示详情，不再区分dataType
				this.editItem(item);
			},
			// 预览AI识别的图片
			previewImage(item) {
				const imageUrl = this.getFoodImageSrc(item);
				if (imageUrl) {
					uni.previewImage({
						urls: [imageUrl],
						current: imageUrl
					});
				}
			},
			// 获取食物图片源
			getFoodImageSrc(item) {
				if (item.dataType == 0 || item.dataType == '0') {
					// 普通录入的食物数据使用默认图片
					return 'https://fat-1323860534.cos.ap-nanjing.myqcloud.com/weixin/defaultFood.jpg';
				} else {
					// AI识别录入或图片数据使用上传的图片
					return this.foodImg(item.eatImg);
				}
			},
			// 获取食物名称
			getFoodName(item) {
				return item.foodName || '';
			},
			// 获取食物详情
			getFoodDetails(item) {
				return `${item.weight || ''}g/${item.energy || ''}kcal`;
			},
			getPatientInfo() {
				PatientApi.patientInfo().then(res => {
					console.log("查询患者中心信息", res)
					if (res.code == 200) {
						this.patientCenter = res.data
						if (this.patientCenter.height) {
							const su = (this.patientCenter.height - 105) * 25
							this.medNutri.energySuggest1 = su
							this.medNutri.proteinSuggest1 = 0;
							this.medNutri.fatSuggest1 = 0;
							this.medNutri.ch2oSuggest1 = 0
						}

					}
				})
			},
			medNutriPercent(a, b) {
				return "30%"
			},
			removeItem() {
				let _this = this;
				uni.showModal({
					title: "确认删除？",
					success(val) {
						if (val.confirm) {
							console.log("del", _this.curItem)
							EatApi.delFoodItem(_this.curItem.id).then(res => {
								if (res.code == 200) {
									uni.showToast({
										title: '删除成功',
										icon: 'none'
									})
									_this.popShow = false;
									_this.getUserEatList()
									// 更新有记录的日期列表
									_this.getRecordedDates()
								}
							})
						}
					}
				})

			},
			addFoodItem() {
				// 校验
				let weight = 0;
				if (this.curItem.weightType == "克") {
					weight = this.curItem.weight;
				} else {
					weight = this.curItem.portionNum * this.showFood.weight
				}
				console.log("当前食物重量是：" + weight)
				if (weight <= 0) {
					uni.showToast({
						title: "输入不能为0",
						error: 'error'
					})
				}
				//查看是否已经添加过
				if (this.foodItems && this.foodItems.length > 0) {
					let item = this.foodItems.filter(item => item.foodId == this.showFood.id);
					if (item.length != 0) {
						console.log("已经添加过了", item)
						this.curItem.id = item[0].id
					}
				}
				this.curItem.weight = weight
				EatApi.saveFoodItem(this.curItem).then(res => {
					console.log(res)
					if (res.code == 200) {
						uni.showToast({
							title: "添加成功"
						})
						this.popShow = false;
						this.curItem.id = null;
						this.getUserEatList()
						// 更新有记录的日期列表
						this.getRecordedDates()
					}
				})

			},
			editItem(item) {
				uni.showLoading({
					title: "数据加载中..."
				})

				// 区分普通录入和AI识别录入
				if (item.dataType == 'AI') {
					// AI识别录入，调用查询fat_eat_item表的接口
					EatApi.getAIFoodItemDetail(item.id).then(res => {
						uni.hideLoading()
						if (res.code == 200) {
							// AI识别的数据直接使用fat_eat_item表中的数据
							this.showFood = {
								id: item.foodId,
								foodName: res.data.foodName,
								weight: res.data.weight,
								energy: res.data.energy,
								protein: res.data.protein,
								fat: res.data.fat,
								ch2o: res.data.ch2o,
								df: res.data.df || 0,
								va: res.data.va || 0,
								vc: res.data.vc || 0,
								ve: res.data.ve || 0,
								ca: res.data.ca || 0,
								pp: res.data.pp || 0,
								kk: res.data.kk || 0,
								na: res.data.na || 0,
								fe: res.data.fe || 0,
								zn: res.data.zn || 0,
								ii: res.data.ii || 0
							}
							this.popShow = true
							this.curItem = {
								...item,
								// 确保AI识别录入的数据有必要的默认值
								weightType: item.weightType || '克',
								weight: item.weight || this.showFood.weight || 100,
								portionNum: item.portionNum || 1
							};
						}
					}).catch(() => {
						uni.hideLoading()
						uni.showToast({
							title: '获取AI识别食物详情失败',
							icon: 'none'
						})
					})
				} else {
					// 普通录入，调用原有的食物库接口
					FoodApi.getFoodInfoById(item.foodId).then(res => {
						uni.hideLoading()
						if (res.code == 200) {
							this.showFood = res.data
							this.popShow = true
							this.curItem = {
								...item,
								// 确保普通录入的数据有必要的默认值
								weightType: item.weightType || '克',
								weight: item.weight || this.showFood.weight || 100,
								portionNum: item.portionNum || 1
							};
						}
					}).catch(() => {
						uni.hideLoading()
						uni.showToast({
							title: '获取食物详情失败',
							icon: 'none'
						})
					})
				}
			},
			jiSuanEnergy(eat) {
				if (eat.fatEatItemList && eat.fatEatItemList.length > 0) {
					const total = eat.fatEatItemList.reduce((t, e) => t + e.energy, 0)
					return total + " kcal"
				} else {
					return "0 kcal";
				}
			},
			removePic(item) {
				console.log(item)
				let _this = this;
				uni.showModal({
					title: "确认删除？",
					success(val) {
						if (val.confirm) {
							EatApi.delFoodItem(item.id).then(res => {
								if (res.code == 200) {
									// _this.getServerData();
									//查询干预里的营养建议
									_this.getUserCurInte()
									//查询用户的餐饮记录
									_this.getUserEatList()
								}
							})
						}
					}
				})
			},
			foodImg(imgUrl) {
				console.log("imgUrl", imgUrl)
				if (imgUrl && !imgUrl.startsWith("http")) {
					return appConfig.getPicUrl() + imgUrl;
				} else if (imgUrl && imgUrl.startsWith("http")) {
					return imgUrl;
				} else {
					return 'https://fat-1323860534.cos.ap-nanjing.myqcloud.com/weixin/defaultFood.jpg';
				}
			},
			getUserEatList() {
				EatApi.getUserEatList({
					eatTime: this.nowDate.format("YYYY-MM-DD")
				}).then(res => {
					console.log("查询用户的餐饮记录", res)
					if (res.code == 200) {
						this.eatStatus = ['N', 'N', 'N', 'N', 'N', 'N']
						this.eatList = res.data
						this.foodItems = []
						this.eatList.forEach(item => {
							this.eatStatus[item.eatType] = item.eatIf
							this.foodItems.push(...item.fatEatItemList)
						})
						let _this = this;
						setTimeout(function() {
							_this.getServerData()
						}, 1500)


					}
				})
			},
			getUserCurInte() {
				EatApi.getUserCurIntervene({
					patientId: this.patientId
				}).then(res => {
					console.log(res)
					if (res.code == 200) {

						if (res.data && res.data.forms && res.data.forms.length > 0) {
							let forms = res.data.forms
							let mnForms = forms.filter(item => item.formKey == 'medical_nutrition')
							if (mnForms.length > 0) {
								let formValue = mnForms[0].formValue
								if (formValue) {
									this.medNutri = JSON.parse(formValue)
									console.log(this.medNutri)
								}
							}
						} else {
							this.getPatientInfo()
						}
					}
				})
			},
			toEatStatics() {
				uni.navigateTo({
					url: '/pageWork/statics/diet'
				})
			},
			editFood(eatType) {
				console.log(eatType)
				uni.navigateTo({
					url: '/pageWork/foodCategory/foodCategory?eatType=' + eatType + "&eatTime=" + this.nowDate
						.format('YYYY-MM-DD')
				})
			},
			formatter(day) {
				// 检查当前日期是否有饮食记录
				const dayStr = dayjs(day.date).format('YYYY-MM-DD')
				if (this.recordedDates.includes(dayStr)) {
					day.dot = true
				}
				return day
			},

			toNutrAnalysis() {
				uni.navigateTo({
					url: "/pageWork/nutrAnalysis/nutrAnalysis?date=" + this.nowDate.format("YYYY-MM-DD")
				})
			},
			toNutrRanking() {
				uni.navigateTo({
					url: "/pageWork/nutrRanking/nutrRanking?date=" + this.nowDate.format("YYYY-MM-DD")
				})
			},
			preDay() {
				this.nowDate = this.nowDate.subtract(1, 'day')
			},
			nextDay() {
				let nextDate = this.nowDate.add(1, 'day')
				console.log(dayjs().diff(nextDate))
				if (dayjs().diff(nextDate) < 0) {
					uni.showToast({
						title: '不能打卡之后的日期'
					})
				} else {
					this.nowDate = this.nowDate.add(1, 'day')
				}
			},
			getServerData() {
				// uni.showLoading({
				// 	title: "数据加载中..."
				// })
				let items = []
				this.eatList.forEach(item => {
					items.push(...item.fatEatItemList)
				})
				console.log("getServerData1", items)
				console.log("getServerData2", this.medNutri)
				if (items.length > 0 && this.medNutri) {
					let sum = items.reduce((t, e) => t + e.energy, 0)
					this.nutrSumData.energy = sum.toFixed(1);
					let sum2 = items.reduce((t, e) => t + e.protein, 0)
					this.nutrSumData.protein = sum2.toFixed(1);
					let sum3 = items.reduce((t, e) => t + e.fat, 0)
					this.nutrSumData.fat = sum3.toFixed(1);
					let sum4 = items.reduce((t, e) => t + e.ch2o, 0)
					this.nutrSumData.ch2o = sum4.toFixed(1);
					console.log(sum, this.medNutri.energySuggest1)
					let a = sum / this.medNutri.energySuggest1
					let res = {
						series: [{
							name: "摄入比",
							color: "#1890FF",
							data: a
						}]
					};
					this.opts.title.name = (a * 100).toFixed(0) + "%"
					this.percent = (a * 100).toFixed(0);
					this.chartData = JSON.parse(JSON.stringify(res));
				} else {
					let res = {
						series: [{
							name: "摄入比",
							color: "#1890FF",
							data: 0
						}]
					};
					this.opts.title.name = 0 + "%"
					this.percent = 0;
					this.nutrSumData = {
						energy: 0,
						protein: 0,
						fat: 0,
						ch2o: 0
					}
					this.chartData = JSON.parse(JSON.stringify(res));
				}
			},
			confirmDate(e) {
				console.log('时间', e);
				this.dateShow = false;
				// 更新 nowDate 的值为选择的日期
				this.nowDate = dayjs(e);
			},
			// 获取有饮食记录的日期列表
			getRecordedDates() {
				// 暂时使用备用方案，从现有接口获取记录日期
				this.extractRecordedDatesFromEatList()
			},
			// 从现有的饮食记录中提取有记录的日期（备用方案）
			async extractRecordedDatesFromEatList() {
				const dates = new Set()

				// 使用分页接口获取最近的饮食记录
				try {
					const res = await EatApi.getUserPageEatList({
						patientId: this.patientId,
						pageNum: 1,
						pageSize: 100 // 获取最近100条记录
					})

					if (res.code == 200 && res.rows) {
						res.rows.forEach(item => {
							if (item.eatIf === 'Y' && item.fatEatItemList && item.fatEatItemList.length > 0) {
								// 提取日期部分（去掉时间）
								const dateStr = item.eatTime ? item.eatTime.split(' ')[0] : null
								if (dateStr) {
									dates.add(dateStr)
								}
							}
						})
						this.recordedDates = Array.from(dates)
						console.log("提取的有记录日期", this.recordedDates)
					}
				} catch (err) {
					console.log("获取饮食记录失败", err)
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* 整体容器样式 */
	.food-commit-container {
		background-color: #f8f8f8;
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	/* 吸顶容器样式 */
	.sticky-top-container {
		position: fixed;
		top: 88px; /* 状态栏(44px) + 导航栏内容(44px) */
		left: 0;
		right: 0;
		z-index: 10089; /* 设置比导航栏稍低的z-index */
		background-color: #f8f8f8;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	/* 头部样式 */
	.foodCommit-header {
		display: flex;
		justify-content: center;
		align-items: center;
		vertical-align: middle;
		background-color: #fff;
		height: 90rpx; /* 减少日期选择器高度 */
		line-height: 90rpx;
		border-bottom: 1px solid #EBEEF5;
		position: relative;
		margin-bottom: 0;
	}

	.date-selector {
		display: flex;
		justify-content: center;
		align-items: center;
		vertical-align: middle;
		margin: 0 25px;
	}

	.date-text {
		margin-left: 10rpx;
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
	}
 
	/* 摘要卡片容器样式 */
	.summary-card-container {
		background-color: #f8f8f8;
		padding: 5rpx 20rpx 15rpx 20rpx; /* 减少上下内边距，让间距更紧凑 */
	}

	/* 摘要卡片样式 */
	.summary-card {
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		margin: 0;
	}

	/* 可滚动内容区域 */
	.scrollable-content {
		flex: 1;
		margin-top: 550rpx; /* 调整顶部间距，适应紧凑的上半部分布局 */
		background-color: #f8f8f8;
		padding-top: 30rpx; /* 适当减少内边距 */
	}

	.stats-icon-container {
		display: flex;
		justify-content: flex-end;
		margin-bottom: 10rpx;
	}

	.nutrition-summary {
		display: flex;
		align-items: center;
		vertical-align: middle;
		justify-content: space-between;
		margin-bottom: 30rpx;
	}

	.summary-item {
		text-align: center;
	}

	.summary-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 10rpx;
		font-weight: normal;
	}

	.summary-value {
		font-size: 36rpx;
		color: #666;
		font-weight: normal;
	}

	.progress-container {
		width: 50%;
		height: 200rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1; /* 确保进度条在较低层级 */
	}

	/* 营养详情样式 */
	.nutrition-details {
		display: flex;
		align-items: center;
		vertical-align: middle;
		justify-content: space-between;
		margin-top: 20rpx;
	}

	.detail-item {
		text-align: center;
		flex: 1;
	}

	.detail-item:nth-child(2) {
		margin: 0 20rpx;
	}

	.detail-label {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.progress-bar {
		height: 14rpx;
		background-color: #eee;
		width: 100%;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.progress-fill {
		height: 14rpx;
		background-color: #4f41ac;
		border-radius: 20rpx;
		transition: width 0.3s ease;
	}

	.detail-value {
		font-size: 26rpx;
		color: #333;
		margin-top: 10rpx;
	}

	/* 食物列表样式 */
	.food-list-container {
		padding: 0 20rpx 20rpx 20rpx; /* 增加底部内边距 */
		position: relative;
		z-index: 1; /* 确保列表在较低层级 */
	}

	.food-card {
		margin-bottom: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.food-item {
		display: flex;
		vertical-align: middle;
		justify-content: space-between;
		align-items: center;
		margin: 16rpx 0;
		padding: 10rpx;
		border-radius: 12rpx;
		transition: background-color 0.3s;
	}

	.food-item:active {
		background-color: #f5f5f5;
	}

	.food-image {
		flex: 1;
	}

	/* AI识别图片样式 */
	.ai-food-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 12rpx;
		overflow: hidden;
		position: relative;
		cursor: pointer;
	}

	.ai-image {
		width: 100%;
		height: 100%;
		border-radius: 12rpx;
	}

	.food-info {
		flex: 5;
		margin-left: 16rpx;
		display: flex;
		flex-direction: column;
	}

	.food-name {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}

	.food-details {
		font-size: 26rpx;
		color: #666;
	}

	.food-action {
		flex: 1;
		text-align: right;
	}



	/* 操作按钮样式 */
	.action-buttons {
		display: flex;
		justify-content: space-around;
		margin: 80rpx 0 180rpx 0;
	}

	.action-button {
		width: 320rpx;
		background-color: #4f41ac;
		border-radius: 50rpx;
		font-size: 30rpx;
		box-shadow: 0 6rpx 16rpx rgba(79, 65, 172, 0.3);
		transition: transform 0.2s;
	}

	.action-button:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(79, 65, 172, 0.3);
	}

	/* 空状态样式 */
	.empty-container {
		margin-top: 50rpx;
		padding: 0 20rpx;
	}

	/* 底部标签栏样式 */
	.meal-tabbar {
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 90; /* 确保底部标签栏在较高层级，但低于头部 */
	}

	.meal-tabs {
		display: flex;
		align-items: center;
		justify-content: space-around;
		vertical-align: middle;
		width: 100%;
		padding: 10rpx 0;
	}

	.meal-tab-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		vertical-align: middle;
		padding: 10rpx;
		transition: transform 0.2s;
	}

	.meal-tab-item:active {
		transform: scale(0.95);
	}

	.meal-icon {
		margin-bottom: 6rpx;
	}

	.meal-name {
		font-size: 26rpx;
		color: #333;
	}

	/* 弹出层样式 */
	.food-popup {
		z-index: 10080;
	}

	.popup-content {
		margin: 40rpx;
		padding: 20rpx;
	}

	.food-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.food-header-info {
		margin-left: 16rpx;
	}

	.food-header-name {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 6rpx;
	}

	.food-header-weight {
		font-size: 26rpx;
		color: #666;
	}

	.portion {
		color: #ff9900;
		margin-right: 6rpx;
	}

	/* 弹出层样式 */
	.popup-content {
		background-color: #fff;
		border-radius: 24rpx 24rpx 0 0;
		overflow: hidden;
	}
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10rpx 10rpx 10rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.popup-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}
	
	.popup-close {
		padding: 10rpx;
	}
	
	/* 营养卡片样式 */
	.nutrition-card {
		background-color: #f8f8f8;
		border-radius: 16rpx;
		padding: 16rpx;
		margin: 0 10rpx 20rpx 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.nutrition-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 12rpx 16rpx;
	}
	
	.nutrition-item {
		display: flex;
		flex-direction: column;
	}
	
	.nutrition-label {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 6rpx;
	}
	
	.nutrition-value {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}
	
	/* 重量输入样式 */
	.weight-input-container {
		background-color: #f8f8f8;
		border-radius: 16rpx;
		padding: 16rpx;
		margin: 0 10rpx 20rpx 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.weight-input-wrapper {
		display: flex;
		align-items: center;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 16rpx;
		margin-bottom: 20rpx;
		border: 1rpx solid #eee;
	}
	
	.weight-input {
		flex: 1;
		height: 50rpx;
		font-size: 26rpx;
		color: #333;
	}
	
	.weight-type-selector {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 10rpx;
	}
	
	.weight-type-label {
		font-size: 24rpx;
		color: #666;
		margin: 0 16rpx;
	}
	
	/* 确认按钮样式 */
	.confirm-button {
		margin: 0;
		border-radius: 12rpx;
		background-color: #4f41ac;
		box-shadow: 0 4rpx 12rpx rgba(79, 65, 172, 0.2);
		color: #ffffff;
	}

	.meal-tabs {
		margin: 0 20rpx;
		color: #333;
	}

	.popup-actions {
		display: flex;
		vertical-align: middle;
		align-items: center;
		margin-top: 40rpx;
	}

	.delete-action {
		flex: 1;
		color: #ff5252;
		font-size: 30rpx;
		text-align: center;
	}

	.confirm-action {
		flex: 5;
	}

	.confirm-button {
		margin: 20rpx 0;
		border-radius: 50rpx;
		background-color: #4f41ac;
		box-shadow: 0 6rpx 16rpx rgba(79, 65, 172, 0.3);
		color: #ffffff;
	}

	.title {
		color: $u-primary;
		text-align: center;
		padding: 20rpx 0 0 0;
	}

	.shareBtn {
		position: absolute;
		background: none;
		right: 5px;
		color: #2979ff;
		font-size: 14px;
		border: none;
	}
</style>