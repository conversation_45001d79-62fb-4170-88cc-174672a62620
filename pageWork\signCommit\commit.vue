<template>
	<view class="commit">
		<Navbar :hideBtn="false" title="体征信息提交" bgColor="#b7bbfa" :h5Show="false" :fixed="true" :titleStyle="{color:'#FFFFFF', fontSize:'16px', fontWeight:'500'}"></Navbar>
		<!-- 	<dynamic-form v-if="show1" :showSubmittBtn="true" @formSubmit = "formSubmit" :screen-form="screenForm"
			:value="screenForm.id"></dynamic-form> -->
		<lxCalendar :SignList="SignList" @change="change"></lxCalendar>
		<Weight v-if="component ==='Weight'" @submit="handleSubmit" :signTime="signTime"></Weight>
		<Sport v-else-if="component ==='Sport'"@submit="handleSubmit" @finishSportEdit="finishSportEdit" :signTime="signTime"></Sport>
		<view class="weight-trend"  v-if="component ==='Sport'">
			<view class="weight-title">
				运动耗能
			</view>
			<view class="weight-unit">
				单位：kcal
			</view>
			<view class="weight-charts" v-if="sportChartData">
				
				<qiun-data-charts type="line" :canvas2d="true" canvasId="sportChajhffddrtData"
					:activeType="hollow"  :chartData="sportChartData" />
			</view>
				<u-empty v-else></u-empty>
			<view class="weight-during">
				<view :class="['weight-during-one',item.id===during&&'weight-during-active']" v-for="item in duringList"
					:key="item.id" @click="changeDuring(item.id)">
					{{item.name}}
				</view>
			</view>
		</view>
		<Waist v-else-if="component ==='Waist'"@submit="handleSubmit" :signTime="signTime"></Waist>
		<BloodPressure v-else-if="component ==='BloodPressure'" @submit="handleSubmit":signTime="signTime"></BloodPressure>
		<BloodSugar v-else-if="component ==='BloodSugar'" @submit="handleSubmit":signTime="signTime"></BloodSugar>
	</view>
</template>

<script>
	import lxCalendar from '@/pageWork/components/lx-calendar/lx-calendar.vue'
	import * as DynamicFormApi from "@/api/dynamicForm.js"
	import * as SignApi from "@/api/signCommit.js"
	import DynamicForm from "@/pageWork/components/uni-active-form/dynamic-form";
	import Weight from "./components/Weight"
	import Waist from "./components/Waist.vue"
	import BloodSugar from "./components/BloodSugar.vue"
	import BloodPressure from "./components/BloodPressure.vue"
	import Sport from "./components/Sport.vue"
	import Navbar from '@/components/navbar/Navbar'
	import dayjs from 'dayjs';

	export default {
		components: {
			lxCalendar,Weight,Waist,BloodSugar,BloodPressure,Sport,Navbar
		},
		data() {
			return {
				show1: false,
				signId: null,
				SignList:[],
				screenForm: {
					'formText': "{\n  \"list\": [  ]\n}"
				},
				signTime:dayjs().format("YYYY-MM-DD"),
				user: {},
				component:'',
				opts: {
					padding: [20, 20, 20, 20],
					xAxis: {
						boundaryGap: 'justify',
						fontColor: '#999'
					},
					yAxis: {
						disabled: true,
						disableGrid: true
					},
					dataPointShapeType: 'hollow',
					legend: {
						show: false
					}
				},
				sportChartData:null,
				during:'day'
			}
		},
		onLoad(option) {
			this.getInfo()
			console.log('option',option)
			this.getDynamicFormInfo(option.id)
			this.signId = option.id
			this.component = option.component
			this.signName=option.signName
			if(this.component=='Sport'){
				this.getChartData("运动")
			}
			this.getSignData()
		},
		methods: {
			handleSubmit(data) {
				console.log('执行')
			  this.getSignData();
			},
			getSignData() {
				console.log('名称',this.signName)
				if (this.signName == '运动量') {
					
					SignApi.getPatientSport2().then(res => {
						if (res.code == 200 && Array.isArray(res.data)) {
							this.SignList = res.data.map(item => ({
								...item, // 复制原数据
								signTime: item.sportDate // 添加 signTime 属性，值为每个对象的 createTime
							}));
							console.log('数据',this.SignList)
						}
					})

				} else {
					let query = {
						signName: this.signName,
						patientId: this.$store.state.unit.unionId
					}
					SignApi.SignList(query).then(res => {
						console.log("体征数据", res)
						if (res.code == 200 && res.rows) {
							this.SignList = []
							this.SignList = res.rows

						}
					})
				}

			},
			finishSportEdit(){
			    console.log("finishSportEdit");	
				if(this.component=='Sport'){
					this.getChartData("运动")
				}
			},
			getChartData(signType) {
				let query = {
					signTime: dayjs().format("YYYY-MM-DD"),
					signType: signType,
					during: this.during,
					patientId:this.$store.state.unit.unionId,
				}
				SignApi.getSignChartData(query).then(res => {
					console.log("近7天" + signType, res)
					if (res.code == 200) {
						if (res.data) {
							let data = res.data;
							let months;
							let values;
							if (this.during == 'day') {
								if (signType == '体重') {
									months = data.map(item => item.signTime.substring(5, 10))
									values = data.map(item => item.weight)
								}
								if (signType == '腰围') {
									months = data.map(item => item.signTime.substring(5, 10))
									values = data.map(item => item.waist)
								}
								if (signType == '运动') {
									months = data.months;
									values = data.values;
								}
			
							}
							console.log(months, values)
							if (signType == '体重') {
								this.weightChartData = {
									"categories": months,
									"series": [{
										"name": "日期",
										"data": values,
										"color": "#4f41ac"
									}]
								}
							}
							if (signType == '腰围') {
								this.waistChartData = {
									"categories": months,
									"series": [{
										"name": "日期",
										"data": values,
										"color": "#4f41ac"
									}]
								}
							}
							if (signType == '运动') {
								this.sportChartData = {
									"categories": months,
									"series": [{
										"name": "日期",
										"data": values,
										"color": "#4f41ac"
									}]
								}
							}
			
						}
					}
				})
			},
			
			change(e) {
				console.log(e);
				this.signTime = e.fulldate
			},
			getInfo() {
				const app = this
				app.$store.dispatch('Info').then(res => {
					app.user = res.user
					console.log(app.user)
				})

			},
			formSubmit(data) {
				let commitData = {
					formValue: JSON.stringify(data),
					signId: this.signId,
					formText: this.screenForm.formText,
					idNo: this.user.userName,
					signName: this.screenForm.formName,
					patientName: this.user.nickName
				}
				console.log(commitData)
				SignApi.signRecordSave(commitData).then(res => {
					console.log(res)
					if (res.code == 200) {

						uni.showToast({
							title: "�1�7�6�3�1�7�0�6�1�7"
						})
						this.Subscribe()
						uni.navigateBack(-1)
					}
				})

			},

			Subscribe() {
				uni.requestSubscribeMessage({
					//�1�7�0�4�1�7�1�7�1�7�Մ1�7�0�8�1�7�1�7�1�7�1�7�1�7�0�0�1�7�1�7�1�7�1�7�0�0�1�7�1�7ID
					tmplIds: ['Je4YBOOfkE0jRYzz5XFVaQCAAStsZnEyNVNN8j8jdzM'],
					success(res) {
						console.log("---------", res)
					},
					fail(res) {
						console.log("@@@@@@", res)
					}
				})
			},
			getDynamicFormInfo(id) {
				DynamicFormApi.getById(id).then(res => {
					console.log(res)
					if (res.code == 200) {
						this.screenForm = res.data
						this.screenForm['formText'] = this.screenForm['formDesignerText']
						this.show1 = true;
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.commit {
		min-height: 100vh;
			background: #f9f9f9;
	}
	
	.weight {
		padding: 30rpx;
	
		&-current {
			padding: 30rpx;
			background: #FFFFFF;
			height: 300rpx;
		}
	
		&-title {
			position: relative;
			z-index: 1;
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
	
			&::after {
	
				position: absolute;
				bottom: -10rpx;
				left: 0rpx;
				content: "";
				z-index: -1;
				width: 160rpx;
				height: 20rpx;
				background: linear-gradient(to right, #4f41ac, transparent);
			}
		}
	
		&-input {
			margin-top: 60rpx;
			display: flex;
			justify-content: center;
			align-items: center;
	
			input {
				width: 300rpx;
				border-bottom: 1px solid #ddd;
				font-size: 60rpx;
				text-align: center;
				padding-bottom: 10rpx;
			}
	
			text {
				font-size: 24rpx;
			}
		}
	
		&-warn {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #999;
			font-size: 24rpx;
			margin-top: 10rpx;
	
			text {
				margin-left: 10rpx;
			}
		}
	
		&-status {
			display: flex;
			margin-top: 50rpx;
			font-size: 30rpx;
			justify-content: center;
	
			view:last-child {
				margin-left: 80rpx;
			}
		}
	
		&-trend {
			padding: 30rpx;
			background: #FFFFFF;
			height: 640rpx;
			margin-top: 30rpx;
			position: relative;
		}
	
		&-during {
			display: flex;
			justify-content: space-around;
	
			&-one {
				background: #f9f9f9;
				color: #000;
				width: 120rpx;
				height: 60rpx;
				text-align: center;
				line-height: 60rpx;
				border-radius: 30rpx;
				font-size: 28rpx;
			}
	
			&-active {
				background: #4f41ac;
				color: #FFF;
			}
		}
	
		&-unit {
			position: absolute;
			right: 30rpx;
			top: 30rpx;
			font-size: 32rpx;
			color: #999;
		}
	}
	
	

</style>
